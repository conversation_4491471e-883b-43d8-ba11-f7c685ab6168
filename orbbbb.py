import vectorbt as vbt
import pandas as pd
import numpy as np
from numba import njit
from datetime import datetime, timedelta

# ---------- Paramètres ----------
# Reduce the time range to comply with Yahoo Finance 1m data limitation (max 8 days)
start_date = '2025-06-01'
end_date = '2025-07-28'  # Limit to 8 days from start date
TIMEFRAME = '15m'  # 15 minute timeframe
# Remove ARKK from the symbols list as it appears to be delisted
symbols = ['AAPL','MSFT','NVDA','TSLA','AMZN','META','GOOGL','NFLX','AMD','CRM',
           'ADBE','PYPL','INTC','CMCSA','PEP','KO','TMO','AVGO','TXN','QCOM',
           'ABNB','UBER','ZM','SNOW','PLTR','COIN','SOFI','RIVN','LCID']

print(f"Downloading data for {len(symbols)} symbols from {start_date} to {end_date}")

try:
    UNIVERSE = vbt.YFData.download(
        symbols=symbols,
        start=start_date, 
        end=end_date,
        interval=TIMEFRAME,
        tz_convert='America/New_York',
        missing_index='drop'  # Handle missing data gracefully
    )
except Exception as e:
    print(f"Error downloading data: {e}")
    raise

# Extract data correctly from YFData object
open_ = UNIVERSE.get('Open')
close = UNIVERSE.get('Close')
high = UNIVERSE.get('High')
low = UNIVERSE.get('Low')
volume = UNIVERSE.get('Volume')

# Ensure we have proper DatetimeIndex
if not isinstance(open_.index, pd.DatetimeIndex):
    open_.index = pd.to_datetime(open_.index)
    close.index = pd.to_datetime(close.index)
    high.index = pd.to_datetime(high.index)
    low.index = pd.to_datetime(low.index)
    volume.index = pd.to_datetime(volume.index)

# --- Paramètres de Risque et de Portefeuille ---
INITIAL_CAPITAL = 100_000
RISK_PER_TRADE = 0.01  # 1% du capital risqué par trade
ATR_STOP_MULT = 2.0    # Le stop-loss est placé à 2x l'ATR

# =============================================================================
# 2. FILTRES QUOTIDIENS ET SÉLECTION DES ACTIFS
# =============================================================================
print("🔍 Filtrage et sélection des actifs...")

# --- Filtres quotidiens pour définir l'univers négociable chaque jour ---
# Grouper par jour pour les calculs quotidiens
daily_open = open_.resample('D').first()
daily_volume = volume.resample('D').sum()
daily_high = high.resample('D').max()
daily_low = low.resample('D').min()
daily_close = close.resample('D').last()

# Filtre 1: Prix d'ouverture quotidien > 5 $
valid_price = daily_open > 5

# Filtre 2: Volume quotidien moyen sur 20 jours >= 1M
# For a shorter period, we might need to adjust this
avg_days = min(5, len(daily_volume))  # Use minimum of 5 days or available data
avg_daily_vol = daily_volume.rolling(avg_days).mean()
valid_vol = avg_daily_vol >= 1_000_000

# Filtre 3: ATR quotidien sur 14 jours > 0.5 $
# For shorter periods, adjust ATR window
atr_window = min(14, len(daily_close))
atr14_daily = vbt.ATR.run(daily_high, daily_low, daily_close, window=atr_window).atr
valid_atr = atr14_daily > 0.5

# Combinaison des filtres pour obtenir les actifs valides chaque jour
universe_ok = (valid_price & valid_vol & valid_atr).ffill()

# --- Classement basé sur le volume relatif des 5 premières minutes ---
# Volume des 5 premières minutes (09:30 à 09:34)
vol_5min = volume.between_time('09:30', '09:34').resample('D').sum()

# Adjust rolling window for shorter periods
vol_ma_window = min(5, len(vol_5min))
vol_5min_ma20 = vol_5min.rolling(vol_ma_window).mean()

# Calcul du volume relatif (RelVol)
# On ajoute un petit epsilon pour éviter la division par zéro
rel_vol = vol_5min / (vol_5min_ma20 + 1e-8)

# On ne garde que le top 10 des actifs avec le plus grand RelVol, parmi l'univers valide
top10 = rel_vol.where(universe_ok).rank(axis=1, method='max', ascending=False) <= 10

# Masque final pour la simulation: Vrai pour un actif s'il est dans le top 10 ce jour-là
selection_mask = top10.reindex(close.index, method='ffill').fillna(False)

# =============================================================================
# 3. PRÉPARATION DES DONNÉES POUR LA SIMULATION
# =============================================================================
print("⚙️ Préparation des données pour la simulation...")

# --- Création des données d'entrée pour le backtest ---
# On ne simule que sur les ticks où au moins un actif est sélectionné pour optimiser
sim_mask = selection_mask.any(axis=1)

sim_open = open_[sim_mask]
sim_close = close[sim_mask]
sim_high = high[sim_mask]
sim_low = low[sim_mask]
sim_selection = selection_mask[sim_mask]

# --- Calcul de l'ATR sur les données 1 minute ---
atr14 = vbt.ATR.run(high, low, close, window=14).atr.bfill()
sim_atr14 = atr14[sim_mask]

# --- (CORRECTION 1) Création du masque temporel pour le signal ---
# On veut entrer à la clôture de la bougie de 09:34
is_signal_time = (sim_close.index.time == pd.to_datetime('09:35').time())

# --- (CORRECTION 2) Création de la série de Stop-Loss dynamique ---
# Le stop-loss en % est dynamique, basé sur l'ATR du moment
# SL (%) = (Distance du SL en $) / (Prix actuel)
sl_distance_dollars = sim_atr14 * ATR_STOP_MULT
sl_stop_pct = sl_distance_dollars / sim_close

# =============================================================================
# 4. FONCTIONS DE SIGNAL ET DE SIZING (NUMBA)
# =============================================================================

@njit
def orb_signal_func_nb(c, selection_arr, is_signal_time_arr, open_arr, high_arr, low_arr, close_arr):
    """
    Génère les signaux d'entrée.
    `c` est un objet contexte fourni par vectorbt.
    """
    i, col = c.i, c.col

    # Condition 1: On ne génère un signal qu'à l'heure définie (09:35)
    if not is_signal_time_arr[i]:
        return (False, False, False, False) # (Entrée Long, Sortie Long, Entrée Short, Sortie Short)

    # Condition 2: L'actif doit faire partie de la sélection du jour
    if not selection_arr[i, col]:
        return (False, False, False, False)

    # Logique de la bougie d'ouverture (s'assurer qu'on a assez de données)
    if i < 1:
        return (False, False, False, False)
        
    # Bougie de 09:30 à 09:34 - comparaison du close actuel avec l'ouverture de la bougie
    # Si le prix clôture au-dessus de l'ouverture = breakout haussier
    # Si le prix clôture sous l'ouverture = breakout baissier
    if close_arr[i, col] > open_arr[i, col]:  # Breakout haussier
        return (True, False, False, False)  # Signal d'achat
    elif close_arr[i, col] < open_arr[i, col]:  # Breakout baissier
        return (False, False, True, False)  # Signal de vente
    
    return (False, False, False, False)

@njit
def size_func_nb(c, capital, risk_pct, atr_val, atr_mult):
    """
    Calcule la taille de la position en se basant sur le risque et l'ATR.
    """
    col = c.col
    # On risque un % fixe du capital total sur chaque trade
    risk_amount_dollars = capital * risk_pct
    
    # La distance au stop-loss est définie par l'ATR
    stop_distance_dollars = atr_val[c.i, col] * atr_mult
    
    if stop_distance_dollars == 0:
        return 0.0

    # Taille de la position = (Montant à risquer) / (Risque par action)
    size_in_shares = risk_amount_dollars / stop_distance_dollars
    
    return size_in_shares

# =============================================================================
# 5. EXÉCUTION DU BACKTEST
# =============================================================================
print("🚀 Exécution du backtest...")

pf = vbt.Portfolio.from_signals(
    close=sim_close,
    open=sim_open,
    high=sim_high,
    low=sim_low,
    # Arguments passés aux fonctions ci-dessus
    signal_args=(
        sim_selection.values,
        is_signal_time,
        sim_open.values,
        sim_high.values,
        sim_low.values,
        sim_close.values
    ),
    sl_stop=sl_stop_pct,
    # Paramètres de portefeuille pour une simulation multi-actifs réaliste
    cash_sharing=True,   # Capital partagé entre tous les actifs
    init_cash=INITIAL_CAPITAL,
    fees=0.001,          # Frais de 0.1% par transaction
    freq=TIMEFRAME
)

# =============================================================================
# 6. AFFICHAGE DES RÉSULTATS
# =============================================================================
print("\n--- 📊 RÉSULTATS DU BACKTEST ---")
print(pf.stats())

print(pf.wrapper.columns)
print("\n--- 📈 Affichage du graphique de performance ---")
# Handle plotting with grouped data limitation
# 'orders' and 'trade_pnl' subplots don't work with grouped data
# We'll plot supported subplots for all assets, and try individual assets for unsupported ones

try:
    # First, plot supported subplots for all assets
    pf.plot(
        subplots=['drawdowns', 'cash', 'value'],
        template='vbt_dark',
        title_text='Performance de la stratégie Opening Range Breakout - Portfolio Analysis'
    ).show()
except Exception as e:
    print(f"Warning: Could not plot portfolio summary - {e}")

# Try to plot individual assets for detailed views
try:
    if len(sim_close.columns) > 0:
        # Select only the first asset for detailed individual plots
        first_asset = sim_close.columns[0]
        print(f"Plotting detailed charts for first asset: {first_asset}")
        
        # Use column parameter to plot a single asset
        pf.plot(
            column=first_asset,
            group_by=False,
            subplots=['orders', 'trade_pnl', 'drawdowns', 'cash', 'value'],
            template='vbt_dark',
            title_text=f'Performance de la stratégie Opening Range Breakout - {first_asset}'
        ).show()
    else:
        print("No assets found to plot")
except Exception as e:
    print(f"Warning: Could not plot individual asset details - {e}")
