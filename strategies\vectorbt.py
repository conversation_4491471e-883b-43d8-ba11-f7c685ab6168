# %%
"""
VectorBT Bollinger Bands Mean Reversion Strategy - FIXED VERSION
Ported from vect.py with exposure issues resolved.

Key fixes applied:
1. Fixed ATR-based position sizing calculation
2. Corrected size array combination for vectorbt
3. Added multiple safety caps to prevent excessive exposure
4. Enhanced risk management with proper stop-loss integration
"""

import pandas as pd
import pandas_ta as ta # utilisé via df.ta
import vectorbt as vbt
import numpy as np
import plotly.graph_objects as go

# Configure vectorbt for better visualization
vbt.settings.set_theme('dark')
vbt.settings['plotting']['layout']['template'] = 'plotly_dark'
vbt.settings['plotting']['layout']['width'] = 1200
vbt.settings['plotting']['layout']['height'] = 200

def run_bollinger_mean_reversion_strategy(data, symbol="ETH/USDC:USDC", timeframe='1h'):
    """
    Run the fixed Bollinger Bands mean reversion strategy
    
    Args:
        data: OHLC DataFrame with columns ['open', 'high', 'low', 'close', 'volume']
        symbol: Trading symbol for display
        timeframe: Timeframe for analysis
    
    Returns:
        dict: Strategy results including portfolio, stats, and signals
    """
    
    # ===== CONFIGURATION =====
    # Mean reversion strategy parameters
    BBANDS_PERIOD = 20         # Period for Bollinger Bands
    BBANDS_STD = 2.0           # Standard deviation for Bollinger Bands
    ADX_PERIOD = 14            # Period for ADX calculation
    ADX_THRESHOLD = 20         # ADX threshold for trend strength
    ADX_THRESHOLD_FILTER = 60  # Filter to avoid trading in directional markets
    SMA_PERIOD = 200           # Period for SMA calculation
    SMA_BOUNCE_THRESHOLD = 0.002  # Threshold for price bounce from SMA (0.2%)
    INITIAL_ENTRY_SIZE = 0.01  # Initial position size (1% of portfolio)
    DCA_SIZE_INCREMENT = 0.01  # Increment for each DCA (1% additional)
    MAX_DCA_SIZE = 0.10  # Maximum DCA size (10% of portfolio)
    EXIT_SIZE = 1.0            # Exit size (100% of position)
    TF = timeframe
    # Portfolio settings
    INITIAL_CASH = 500000
    FEE = 0.0004
    # NEW SIZING / RISK PARAMETERS
    ATR_PERIOD = 14          # ATR window for volatility
    ATR_MULT   = 1.0         # Multiple of ATR used as stop distance
    RISK_PCT   = 0.02        # % of portfolio equity risked per entry
    # NEW: Maximum exposure allowed per side (long or short) at any time
    MAX_SIDE_EXPOSURE = 0.30  # 30% of portfolio

    # ===== DATA PREPARATION =====
    data = data.copy()
    data.columns = [c.lower() for c in data.columns]
    
    required_cols = ['close', 'low', 'high']
    if not all(col in data.columns for col in required_cols):
        raise ValueError(f"Missing required columns. Got: {data.columns.tolist()}")

    # ===== TECHNICAL INDICATORS =====
    # Add indicators using pandas_ta
    # 1. Bollinger Bands
    data.ta.bbands(length=BBANDS_PERIOD, std=BBANDS_STD, append=True)
    
    # 2. ADX - Average Directional Index for trend strength
    data.ta.adx(length=ADX_PERIOD, append=True)
    
    # 3. SMA 200 - Support/Resistance level
    data.ta.sma(length=SMA_PERIOD, append=True)
    # NEW: ATR for volatility-based sizing
    data.ta.atr(length=ATR_PERIOD, append=True)
    
    # Clean up NaN values
    data.dropna(inplace=True)
    data.reset_index(drop=True, inplace=True)  # Reset index after dropping NaNs
    
    # Define column names for readability
    bbl_col = f'BBL_{BBANDS_PERIOD}_{BBANDS_STD}'
    bbm_col = f'BBM_{BBANDS_PERIOD}_{BBANDS_STD}'
    bbu_col = f'BBU_{BBANDS_PERIOD}_{BBANDS_STD}'
    adx_col = f'ADX_{ADX_PERIOD}'
    sma_col = f'SMA_{SMA_PERIOD}'
    # Column name for ATR (detect dynamically because pandas_ta may change format)
    atr_candidates = [col for col in data.columns if col.upper().startswith('ATR')]
    if not atr_candidates:
        raise ValueError("ATR column not found after indicator calculation. Check pandas_ta version.")
    atr_col = atr_candidates[0]

    print(f"\n===== Technical Indicators for {symbol} =====")
    indicator_cols = [bbl_col, bbm_col, bbu_col, adx_col, sma_col]
    print(data[['close'] + indicator_cols].tail(10))

    # ===== ENTRY/EXIT SIGNALS =====
    # --- LONG & SHORT STRATEGIES ---
    # Trend condition for adaptive exits
    weak_trend = data[adx_col] < ADX_THRESHOLD
    strong_trend = ~weak_trend  # Opposite of weak trend
    # Filter condition for high ADX - avoid trading in strongly directional markets
    high_adx_filter = data[adx_col] >= ADX_THRESHOLD_FILTER
    
    # Define entry conditions
    # Initial entries: Price crosses from below to above the lower band for longs
    # AND ADX is below the filter threshold
    long_initial_entries = (data['close'].shift(1) < data[bbl_col].shift(1)) & (data['close'] >= data[bbl_col]) & (~high_adx_filter)
    
    # DCA conditions for longs: Price touches or goes below lower band
    # AND ADX is below the filter threshold
    long_dca_conditions = (data['low'] <= data[bbl_col]) & (~high_adx_filter)
    
    # Initial entries: Price crosses from above to below the upper band for shorts
    # AND ADX is below the filter threshold
    short_initial_entries = (data['close'].shift(1) > data[bbu_col].shift(1)) & (data['close'] <= data[bbu_col]) & (~high_adx_filter)
    
    # DCA conditions for shorts: Price touches or goes above upper band
    # AND ADX is below the filter threshold
    short_dca_conditions = (data['high'] >= data[bbu_col]) & (~high_adx_filter)
    
    # Initialize arrays for position tracking
    long_position = pd.Series(0, index=data.index)  # 0 = no position, 1 = in position
    short_position = pd.Series(0, index=data.index)  # 0 = no position, 1 = in position
    
    # Initialize signal arrays
    long_entries = pd.Series(False, index=data.index)
    short_entries = pd.Series(False, index=data.index)
    long_exits = pd.Series(False, index=data.index)
    short_exits = pd.Series(False, index=data.index)

    # Track positions, DCA opportunities, and generate clean signals in a single pass
    for i in range(len(data)):
        # Carry forward position state from previous bar (if not first bar)
        if i > 0:
            long_position.iloc[i] = long_position.iloc[i-1]
            short_position.iloc[i] = short_position.iloc[i-1]
            # NEW: Prevent overlapping exposure – close the opposite side if a fresh initial entry appears
            if long_position.iloc[i] > 0 and short_initial_entries.iloc[i]:
                long_exits.iloc[i] = True
                long_position.iloc[i] = 0  # Close long before opening short
            elif short_position.iloc[i] > 0 and long_initial_entries.iloc[i]:
                short_exits.iloc[i] = True
                short_position.iloc[i] = 0  # Close short before opening long
        
        # Define exit conditions - UPDATED to exit on:
        # 1. Price reaching the opposite band
        # 2. Price reaching middle line if ADX is low (weak trend)
        # 3. ADX exceeding the filter threshold (strong directional market)
        long_exit_condition = (data['close'].iloc[i] >= data[bbu_col].iloc[i]) | \
                              ((data['close'].iloc[i] >= data[bbm_col].iloc[i]) & weak_trend.iloc[i]) | \
                              high_adx_filter.iloc[i]  # Exit if ADX exceeds filter threshold
        
        short_exit_condition = (data['close'].iloc[i] <= data[bbl_col].iloc[i]) | \
                               ((data['close'].iloc[i] <= data[bbm_col].iloc[i]) & weak_trend.iloc[i]) | \
                               high_adx_filter.iloc[i]  # Exit if ADX exceeds filter threshold
        
        # Process positions in priority order: exits first, then entries
        
        # Process exits (only if we have a position)
        if long_position.iloc[i] > 0 and long_exit_condition:
            long_exits.iloc[i] = True
            long_position.iloc[i] = 0  # Close position
        
        if short_position.iloc[i] > 0 and short_exit_condition:
            short_exits.iloc[i] = True
            short_position.iloc[i] = 0  # Close position
        
        # Process initial entries (only if we don't have a position)
        if long_position.iloc[i] == 0 and long_initial_entries.iloc[i]:
            long_entries.iloc[i] = True
            long_position.iloc[i] = 1  # Open position
        
        if short_position.iloc[i] == 0 and short_initial_entries.iloc[i]:
            short_entries.iloc[i] = True
            short_position.iloc[i] = 1  # Open position
        
        # Process DCA opportunities (only if we already have a position and no exit signal on same bar)
        if long_position.iloc[i] > 0 and not long_exits.iloc[i] and long_dca_conditions.iloc[i]:
            # Only add DCA if we didn't already have an entry on this bar
            if not long_entries.iloc[i]:
                long_entries.iloc[i] = True  # Add to position
        
        if short_position.iloc[i] > 0 and not short_exits.iloc[i] and short_dca_conditions.iloc[i]:
            # Only add DCA if we didn't already have an entry on this bar
            if not short_entries.iloc[i]:
                short_entries.iloc[i] = True  # Add to position

    # Count entries and exits by type
    long_initial_count = sum(long_initial_entries & long_entries)
    long_dca_count = sum(long_entries) - long_initial_count
    short_initial_count = sum(short_initial_entries & short_entries)
    short_dca_count = sum(short_entries) - short_initial_count
    
    # Print signal statistics
    print(f"\n===== Signal Statistics for {symbol} =====")
    print(f"Long entries: {long_initial_count}")
    print(f"Long exits: {sum(long_exits)}")
    print(f"Short entries: {short_initial_count}")
    print(f"Short exits: {sum(short_exits)}")

    return {
        'data': data,
        'long_entries': long_entries,
        'short_entries': short_entries,
        'long_exits': long_exits,
        'short_exits': short_exits,
        'atr_col': atr_col,
        'config': {
            'INITIAL_CASH': INITIAL_CASH,
            'RISK_PCT': RISK_PCT,
            'ATR_MULT': ATR_MULT,
            'DCA_SIZE_INCREMENT': DCA_SIZE_INCREMENT,
            'MAX_DCA_SIZE': MAX_DCA_SIZE,
            'MAX_SIDE_EXPOSURE': MAX_SIDE_EXPOSURE,
            'FEE': FEE,
            'TF': TF
        }
    }


def calculate_atr_based_size_vectorized(entries, exits, close, atr, initial_cash, risk_pct, atr_mult,
                                       dca_increment, max_dca_size, max_exposure_pct):
    """
    FIXED: Fully vectorized calculation of position sizes based on ATR and risk management

    Key fixes:
    1. Proper ATR-based sizing calculation (no more massive positions)
    2. Enhanced safety caps to prevent runaway exposure
    3. Correct DCA scaling logic
    """

    # 1. Calculate risk and base size - FIXED CALCULATION
    risk_value = risk_pct * initial_cash

    # CRITICAL FIX: Calculate position size directly in dollar value
    # Position size = Risk Amount / (Stop Distance as % of price)
    stop_distance_pct = np.where(
        (atr > 0) & ~np.isnan(atr) & (close > 0),
        (atr_mult * atr) / close,  # Stop distance as percentage of price
        0.01  # Default 1% stop if ATR is invalid
    )

    # Size in dollar value directly (not units * price)
    base_size_value = np.where(
        stop_distance_pct > 0,
        risk_value / stop_distance_pct,  # Risk amount / stop distance %
        0
    )

    # 2. Vectorize DCA counter - FIXED
    # A new trade starts after each exit. Use cumsum() to create trade ID.
    trade_id = exits.cumsum().shift(1).fillna(0)

    # Only count entries when signal is True
    entries_only = entries.where(entries, 0).astype(int)

    # Group by trade ID and do cumulative sum to get DCA counter
    dca_count = entries_only.groupby(trade_id).cumsum()

    # 3. Apply DCA scaling logic
    # Size increases with (dca_counter - 1) * increment
    dca_multiplier = 1 + (dca_count - 1) * dca_increment

    # Calculate size with DCA scaling
    scaled_size_value = base_size_value * dca_multiplier

    # 4. Apply caps - ENHANCED SAFETY
    max_dca_value = max_dca_size * initial_cash
    max_exposure_value = max_exposure_pct * initial_cash

    # CRITICAL: Cap individual entries more aggressively
    final_size = scaled_size_value.clip(upper=max_dca_value)
    final_size = final_size.clip(upper=max_exposure_value)

    # ADDITIONAL SAFETY: Cap at 5% of portfolio per entry as absolute maximum
    absolute_max = 0.05 * initial_cash
    final_size = final_size.clip(upper=absolute_max)

    # Ensure size is 0 where there's no entry
    final_size[~entries] = 0

    return final_size


def create_portfolio_from_signals(signals_dict):
    """
    Create vectorbt portfolio from strategy signals with FIXED sizing

    Args:
        signals_dict: Dictionary returned from run_bollinger_mean_reversion_strategy

    Returns:
        vbt.Portfolio: Portfolio object with proper exposure control
    """
    data = signals_dict['data']
    long_entries = signals_dict['long_entries']
    short_entries = signals_dict['short_entries']
    long_exits = signals_dict['long_exits']
    short_exits = signals_dict['short_exits']
    atr_col = signals_dict['atr_col']
    config = signals_dict['config']

    # Calculate sizes for both long and short using FIXED vectorized function
    long_size = calculate_atr_based_size_vectorized(
        long_entries, long_exits, data['close'], data[atr_col],
        config['INITIAL_CASH'], config['RISK_PCT'], config['ATR_MULT'],
        config['DCA_SIZE_INCREMENT'], config['MAX_DCA_SIZE'], config['MAX_SIDE_EXPOSURE']
    )

    short_size = calculate_atr_based_size_vectorized(
        short_entries, short_exits, data['close'], data[atr_col],
        config['INITIAL_CASH'], config['RISK_PCT'], config['ATR_MULT'],
        config['DCA_SIZE_INCREMENT'], config['MAX_DCA_SIZE'], config['MAX_SIDE_EXPOSURE']
    )

    # CRITICAL FIX: When using separate long/short signals, sizes must be positive
    # vectorbt handles the direction through the signal type, not size signs
    size_array = np.where(long_entries, long_size, 0) + np.where(short_entries, short_size, 0)

    print("\n===== Portfolio Usage Statistics =====")
    print(f"Maximum long size used: {long_size.max() / config['INITIAL_CASH']:.2%}")
    print(f"Maximum short size used: {short_size.max() / config['INITIAL_CASH']:.2%}")
    print(f"Maximum combined exposure: {(long_size + short_size).max() / config['INITIAL_CASH']:.2%}")

    print("\n===== Risk Management Validation =====")
    print(f"ATR-based stop distance: {config['ATR_MULT']} * ATR")
    print(f"Risk per trade: {config['RISK_PCT']:.1%} of portfolio")
    print("✅ Stop-loss now integrated in portfolio - risk calculation aligned with exits")

    # ===== PORTFOLIO CREATION =====
    # Create a single portfolio that handles both long and short positions using vectorbt's built-in functionality
    # CRITICAL FIX: Add explicit stop-loss based on ATR to align with risk calculation
    portfolio = vbt.Portfolio.from_signals(
        close=data['close'],
        entries=long_entries,                # Long entries
        exits=long_exits,                    # Long exits
        short_entries=short_entries,         # Short entries
        short_exits=short_exits,             # Short exits
        size=size_array,                     # Variable position size based on ATR
        size_type='value',                   # Size expressed in dollar value
        # --- CRITICAL: ADD STOP-LOSS BASED ON ATR ---
        sl_stop=data[atr_col] * config['ATR_MULT'] / data['close'],  # Stop in % of ATR distance
        sl_trail=False,                      # Set True for trailing stop
        # --------------------------------------------
        init_cash=config['INITIAL_CASH'],
        fees=config['FEE'],
        freq=config['TF'],
        accumulate=True                      # Allow DCA (mean reversion strategy uses multiple entries)
    )

    return portfolio


def calculate_beta(strategy_ret: pd.Series, benchmark_ret: pd.Series) -> float:
    """
    Calculate beta coefficient between strategy and benchmark returns.
    Beta = Cov(strategy, benchmark) / Var(benchmark).
    """
    aligned = pd.concat([strategy_ret, benchmark_ret], axis=1).dropna()
    if aligned.shape[0] < 2:
        return np.nan
    cov = np.cov(aligned.iloc[:, 0], aligned.iloc[:, 1])[0, 1]
    var = np.var(aligned.iloc[:, 1])
    return cov / var if var > 0 else np.nan


def analyze_portfolio_performance(portfolio, data, symbol="ETH/USDC:USDC"):
    """
    Comprehensive portfolio analysis with enhanced statistics

    Args:
        portfolio: vbt.Portfolio object
        data: OHLC DataFrame
        symbol: Trading symbol for display

    Returns:
        pd.Series: Enhanced portfolio statistics including beta
    """
    print(f"\n===== Portfolio Stats for {symbol} =====")
    stats = portfolio.stats()

    # Calculate beta and add to stats
    strategy_returns = portfolio.returns()
    asset_returns = data['close'].pct_change()
    beta = calculate_beta(strategy_returns, asset_returns)

    # Create a series for beta with the same format as other stats
    beta_stat = pd.Series(beta, index=['Beta vs Benchmark'])
    stats = pd.concat([stats, beta_stat])

    print(stats)

    # Analyze order execution
    print(f"\n===== Portfolio Trade Analysis for {symbol} =====")
    print(f"Total trades: {len(portfolio.trades)}")

    return stats


def create_strategy_dashboard(portfolio, data, symbol="ETH/USDC:USDC"):
    """
    Create comprehensive strategy dashboard with fixed plotting

    Args:
        portfolio: vbt.Portfolio object
        data: DataFrame with OHLC and indicators
        symbol: Trading symbol for display
    """
    print(f"\n{'='*60}")
    print(f"📊 ADVANCED STRATEGY DASHBOARD - {symbol}")
    print(f"{'='*60}")

    # Start with portfolio plotting using advanced features (fixed subplot list)
    fig = portfolio.plot(
        subplots=['trade_pnl', 'orders', 'drawdowns'],  # Remove 'indicators' - not valid
        make_subplots_kwargs={
            'vertical_spacing': 0.05
        }
    )

    # Add OHLC data with advanced styling
    fig = data.vbt.ohlcv.plot(
        plot_type='candlestick',
        fig=fig,
        show_volume=False,
        xaxis_rangeslider_visible=False
    )

    # Add Bollinger Bands indicators
    bb_cols = [col for col in data.columns if 'BBL_' in col or 'BBM_' in col or 'BBU_' in col]
    sma_cols = [col for col in data.columns if 'SMA_' in col]
    indicator_cols = bb_cols + sma_cols

    if indicator_cols:
        colors = ['yellow', 'orange', 'cyan', 'magenta', 'lime']
        for i, col in enumerate(indicator_cols[:5]):  # Limit to 5 indicators
            fig.add_trace(
                go.Scatter(
                    x=data.index,
                    y=data[col],
                    mode='lines',
                    name=col.replace('_', ' '),
                    line=dict(color=colors[i % len(colors)], width=1.5),
                    opacity=0.8
                ),
                row=1, col=1
            )

    # Advanced layout configuration
    fig.update_layout(
        height=1400,
        width=None,
        title=f'📊 {symbol} - Advanced Strategy Analysis'
    )

    fig.show()
    return fig


# ===== COMPLETE EXAMPLE USAGE =====
def run_complete_analysis(data, symbol="ETH/USDC:USDC", timeframe='1h', show_dashboard=True):
    """
    Complete end-to-end analysis of the Bollinger Bands mean reversion strategy

    Args:
        data: OHLC DataFrame
        symbol: Trading symbol
        timeframe: Analysis timeframe
        show_dashboard: Whether to display the interactive dashboard

    Returns:
        dict: Complete analysis results
    """
    print(f"\n🚀 Starting Complete Analysis for {symbol}")
    print("="*60)

    # Step 1: Generate signals
    print("📊 Step 1: Generating trading signals...")
    signals = run_bollinger_mean_reversion_strategy(data, symbol, timeframe)

    # Step 2: Create portfolio
    print("💼 Step 2: Creating portfolio with fixed sizing...")
    portfolio = create_portfolio_from_signals(signals)

    # Step 3: Analyze performance
    print("📈 Step 3: Analyzing performance...")
    stats = analyze_portfolio_performance(portfolio, signals['data'], symbol)

    # Step 4: Create dashboard (optional)
    if show_dashboard:
        print("📊 Step 4: Creating interactive dashboard...")
        fig = create_strategy_dashboard(portfolio, signals['data'], symbol)
    else:
        fig = None

    print(f"\n✅ Analysis complete for {symbol}!")
    print(f"📊 Max Gross Exposure: {stats.get('Max Gross Exposure [%]', 'N/A'):.2f}%")
    print(f"📈 Total Return: {stats.get('Total Return [%]', 'N/A'):.2f}%")
    print(f"📉 Max Drawdown: {stats.get('Max Drawdown [%]', 'N/A'):.2f}%")
    print(f"⚡ Sharpe Ratio: {stats.get('Sharpe Ratio', 'N/A'):.2f}")

    return {
        'signals': signals,
        'portfolio': portfolio,
        'stats': stats,
        'dashboard': fig,
        'symbol': symbol,
        'timeframe': timeframe
    }
