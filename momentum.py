# %%
# VOLATILITY MOMENTUM STRATEGY - Complete & Restructured
import pandas as pd
import numpy as np
import vectorbt as vbt
import pandas_ta as ta #utilisé a travers df
import os
from plotly.subplots import make_subplots
import plotly.graph_objects as go
import itertools  # besoin pour le grid search

vbt.settings.set_theme('dark')

# =============================================================================
# 1. CONFIGURATION
# =============================================================================
CONFIG = {
    'portfolio': {
        'init_cash': 10000,
        'risk_pct': 0.01,
        'high_vol_risk_pct': 0.005,
        'fees': 0.001,
        'vol_adjust': True,
        'slippage': 0.0005,  # additional slippage of 0.05%
        'profit_target_percent': 0.05,  # NEW
        'atr_multiplier': 2.0  # NEW for trailing stop
    },
    'data': {
        'symbols': ['EURUSD_1H_2009-2025'],  # Noms de base pour les fichiers CSV
        'timeframes': ['1h'],  # Intervalles à charger (peut être plusieurs, ex: ['1h', '4h'])
        'higher_timeframe': None,  # Timeframe supérieur pour le filtre (ex: '4h' ; None pour désactiver)
        'csv_directory': 'data',  # Répertoire pour les fichiers CSV
    },
    'optimization': {
        'volatility_windows': [10, 15, 20, 25, 30],
        'volatility_momentum_windows': [3, 5, 7, 10],
        'volatility_momentum_thresholds': [0.0005, 0.001, 0.0015, 0.002],
        'higher_wma_windows': [20, 50, 100],  # Fenêtres WMA à optimiser pour le filtre higher timeframe
        'split_ratio': 0.7,
        'metric': 'Sharpe Ratio',
    },
    'analysis': {
        'permutation_runs': 50,
        'monte_carlo_runs': 50,
        'noise_std': 0.01
    },
    'indicators': {
        'volatility_momentum_window': 5,
        'volatility_momentum_threshold': 0.001,
        'volatility_reversal_threshold': 0,
        'price_wma_window': 50,
        'atr_length': 14,
    }
}

# Les paramètres d'optimisation sont déjà définis dans CONFIG ci-dessus – suppression des redéfinitions redondantes

# =============================================================================
# 2. CORE FUNCTIONS
# =============================================================================
def get_scalar(value):
    """Safely extract a scalar from common pandas objects or scalars."""
    # DataFrame → first value
    if isinstance(value, pd.DataFrame):
        if value.empty:
            return np.nan
        return value.values.flatten()[0]

    # Series → first element
    if isinstance(value, pd.Series):
        if value.empty:
            return np.nan
        return value.iloc[0]

    # Already a scalar
    return value

def compute_vol_momentum(returns: pd.Series, vol_window: int, vol_momentum_window: int):
    """
    Calcule le momentum de la volatilité comme la différence entre la volatilité actuelle
    et la volatilité d'il y a vol_momentum_window périodes.
    La volatilité est mesurée comme l'écart-type (standard deviation) des returns.
    """
    volatility = returns.rolling(vol_window).std()
    return volatility - volatility.shift(vol_momentum_window)

def compute_wma(close: pd.Series, window: int):
    return ta.wma(close, length=window)

def fetch_data_from_csv(symbol, interval):
    """
    Charge les données historiques depuis un fichier CSV local.
    Le fichier doit être nommé `SYMBOL_INTERVAL.csv` (ex: EURUSD_X_1d.csv).
    """
    csv_dir = CONFIG['data'].get('csv_directory', 'data/historical')
    os.makedirs(csv_dir, exist_ok=True)
    
    # Nettoie le nom du symbole pour le nom de fichier
    safe_symbol = symbol.replace('/', '_').replace('=', '_')
    file_path = os.path.join(csv_dir, f"{safe_symbol}.csv")
    
    if not os.path.exists(file_path):
        print(f"❌ Fichier non trouvé: {file_path}")
        print("   Veuillez placer vos fichiers de données dans le répertoire spécifié.")
        return pd.DataFrame()
        
    print(f"📂 Chargement des données CSV depuis {file_path}...")
    
    try:
        # Try to read with tab separator first, then comma, assuming no header
        try:
            df = pd.read_csv(file_path, sep='\t', header=None, index_col=0, parse_dates=True)
        except:
            try:
                df = pd.read_csv(file_path, header=None, index_col=0, parse_dates=True)
            except:
                # Fallback: try with header in case file has proper headers
                df = pd.read_csv(file_path, index_col=0, parse_dates=True)
        
        # If columns are numeric (no header), assign standard OHLCV names
        if all(isinstance(col, (int, float)) for col in df.columns) or df.columns.tolist() == list(range(len(df.columns))):
            if len(df.columns) >= 4:
                df.columns = ['open', 'high', 'low', 'close'] + [f'col_{i}' for i in range(4, len(df.columns))]
            else:
                print(f"❌ Pas assez de colonnes dans {file_path}. Au moins 4 colonnes requises (OHLC)")
                return pd.DataFrame()
        else:
            # Normalise les colonnes en minuscules
            df.columns = [c.lower() for c in df.columns]

        # S'assure que les colonnes requises sont présentes
        required_cols = ['open', 'high', 'low', 'close']
        if not all(col in df.columns for col in required_cols):
            print(f"❌ Colonnes manquantes dans {file_path}. Requises: {required_cols}")
            print(f"   Colonnes trouvées: {list(df.columns)}")
            return pd.DataFrame()

        # Filtre par plage de dates
        if df.empty:
            print(f"⚠️ Aucune donnée trouvée dans {file_path}")
            return pd.DataFrame()
        
        print(f"✅ Données chargées: {len(df)} barres de {df.index[0]} à {df.index[-1]}")
        return df

    except Exception as e:
        print(f"❌ Erreur lors de la lecture du fichier CSV {file_path}: {e}")
        return pd.DataFrame()

def add_indicators(df):
    """
    Ajoute les indicateurs techniques nécessaires à la stratégie:
    - returns: Changement de prix en pourcentage
    - volatility: Écart-type des returns (mesure de la volatilité)
    - volatility_momentum: Différence entre la volatilité actuelle et celle d'il y a X périodes
    - wma: Weighted Moving Average du prix
    - atr: Average True Range pour le trailing stop
    """
    df['returns'] = df['close'].pct_change()
    df['volatility'] = df['returns'].rolling(CONFIG['indicators']['volatility_momentum_window']).std()
    df['volatility_momentum'] = df['volatility'] - df['volatility'].shift(CONFIG['indicators']['volatility_momentum_window'])
    df['wma'] = ta.wma(df['close'], length=CONFIG['indicators']['price_wma_window'])
    df.ta.atr(length=CONFIG['indicators']['atr_length'], append=True)
    return df.dropna()

def generate_signals(data, params, timeframe_to_use, higher_timeframe=None):
    """
    Génère les signaux d'entrée et de sortie basés sur:
    1. Momentum de la volatilité > seuil
    2. Alignement du prix avec la WMA (au-dessus pour long, en-dessous pour short)
    3. Sortie quand le momentum de la volatilité passe sous le seuil de reversal
    4. Filtre cross-timeframe: Vérifier si le prix (higher) > WMA (higher) pour autoriser les trades
    """
    volatility_window, volatility_momentum_window, volatility_momentum_threshold, higher_wma_window = params
    volatility_window = int(volatility_window)
    volatility_momentum_window = int(volatility_momentum_window)

    # Si un higher timeframe est défini, calculer la WMA sur ce timeframe et la resampler sur le timeframe principal
    def get_higher_filter(df_primary, df_higher, higher_wma_window):
        wma_higher = compute_wma(df_higher['close'], higher_wma_window)
        # Resampler la WMA higher sur l'index du timeframe principal (forward fill pour les valeurs manquantes)
        wma_higher_resampled = wma_higher.reindex(df_primary.index, method='ffill')
        return df_primary['close'] > wma_higher_resampled

    signals = {}
    for symbol, df_dict in data.items(): # data est maintenant {symbole: {timeframe: df}}
        df = df_dict[timeframe_to_use] # Extraction du DataFrame pour le timeframe spécifié

        # Appliquer le filtre cross-timeframe si activé
        higher_filter = pd.Series(True, index=df.index)  # Par défaut: toujours vrai
        if higher_timeframe and higher_timeframe in df_dict:
            df_higher = df_dict[higher_timeframe]
            higher_filter = get_higher_filter(df, df_higher, higher_wma_window)
        
        volatility_momentum = compute_vol_momentum(df['returns'], vol_window=volatility_window, vol_momentum_window=volatility_momentum_window)
        wma = compute_wma(df['close'], CONFIG['indicators']['price_wma_window'])

        long_entries = (volatility_momentum > volatility_momentum_threshold) & (df['close'] > wma) & higher_filter
        short_entries = (volatility_momentum > volatility_momentum_threshold) & (df['close'] < wma) & higher_filter

        volatility_reversal = volatility_momentum < CONFIG['indicators']['volatility_reversal_threshold']

        signals[symbol] = { # Les signaux sont toujours par symbole, pas par symbole_timeframe
                'long_entries': long_entries,
                'long_exits': volatility_reversal,
                'short_entries': short_entries,
                'short_exits': volatility_reversal,
                'volatility_momentum': volatility_momentum
            }
    return signals

def run_backtest(data, params, timeframe_to_use, higher_timeframe=None, freq=None):
    freq = freq or timeframe_to_pandas_freq(timeframe_to_use)
    
    # Convert to pandas offset alias accepted by VectorBT
    # freq = timeframe_to_pandas_freq(freq)

    signals = generate_signals(data, params, timeframe_to_use, higher_timeframe)
    
    close_prices = pd.concat({s: df_dict[timeframe_to_use]['close'] for s, df_dict in data.items()}, axis=1)
    long_entries = pd.concat({s: sig['long_entries'] for s, sig in signals.items()}, axis=1)
    long_exits = pd.concat({s: sig['long_exits'] for s, sig in signals.items()}, axis=1)
    short_entries = pd.concat({s: sig['short_entries'] for s, sig in signals.items()}, axis=1)
    short_exits = pd.concat({s: sig['short_exits'] for s, sig in signals.items()}, axis=1)

    # Position sizing – similar
    atr_mult = CONFIG['portfolio']['atr_multiplier']
    base_risk_pct = CONFIG['portfolio']['risk_pct']

    size_df = pd.DataFrame(index=close_prices.index, columns=close_prices.columns, dtype=float)
    sl_stop_df = pd.DataFrame(index=close_prices.index, columns=close_prices.columns, dtype=float)

    for sym, df_dict in data.items():
        df_sym = df_dict[timeframe_to_use] # Obtenir le DataFrame spécifique au timeframe
        recent_vol = df_sym['close'].pct_change().rolling(20).std().iloc[-1] # Utilisez df_sym ici
        adj_risk_pct = base_risk_pct
        if CONFIG['portfolio']['vol_adjust'] and recent_vol > 0:
            adj_risk_pct *= (0.01 / recent_vol)
        adj_risk_pct = max(0.001, min(0.25, adj_risk_pct))

        risk_per_trade = CONFIG['portfolio']['init_cash'] * adj_risk_pct

        atr_series = df_sym[f"ATRr_{CONFIG['indicators']['atr_length']}"].reindex_like(close_prices[sym]).ffill().bfill()

        units_series = (risk_per_trade / (atr_series * atr_mult)).clip(lower=0)
        size_df[sym] = units_series

        sl_stop_df[sym] = (atr_series * atr_mult) / close_prices[sym]

    risk_size = size_df

    portfolio = vbt.Portfolio.from_signals(
        close=close_prices,
        entries=long_entries,
        exits=long_exits,
        short_entries=short_entries,
        short_exits=short_exits,
        size=risk_size,
        size_type='amount',
        init_cash=CONFIG['portfolio']['init_cash'],
        fees=CONFIG['portfolio']['fees'],
        slippage=CONFIG['portfolio']['slippage'],
        sl_stop=sl_stop_df,
        sl_trail=True,
        tp_stop=CONFIG['portfolio']['profit_target_percent'],  # NEW
        freq=freq,
        group_by=True
    )
    
    return portfolio, signals

# =============================================================================
# 4. GRID-SEARCH OPTIMISATION
# =============================================================================
def grid_search_optimize(data, primary_timeframe, higher_timeframe=None):
    """Find the best parameters via brute-force grid search."""

    param_grid = itertools.product(
        CONFIG['optimization']['volatility_windows'],
        CONFIG['optimization']['volatility_momentum_windows'],
        CONFIG['optimization']['volatility_momentum_thresholds'],
        CONFIG['optimization']['higher_wma_windows']
    )

    results = []
    best_sharpe = -np.inf
    best_params = None

    total_comb = (
        len(CONFIG['optimization']['volatility_windows']) *
        len(CONFIG['optimization']['volatility_momentum_windows']) *
        len(CONFIG['optimization']['volatility_momentum_thresholds']) *
        len(CONFIG['optimization']['higher_wma_windows'])
    )

    comb_idx = 0
    for volatility_window, volatility_momentum_window, volatility_momentum_threshold, higher_wma_window in param_grid:
        comb_idx += 1
        print(f"↪️  Testing combo {comb_idx}/{total_comb}: volatility_window={volatility_window}, volatility_momentum_window={volatility_momentum_window}, volatility_momentum_threshold={volatility_momentum_threshold}, higher_wma_window={higher_wma_window}", end="\r")

        try:
            portfolio, _ = run_backtest(data, (volatility_window, volatility_momentum_window, volatility_momentum_threshold, higher_wma_window), primary_timeframe, higher_timeframe)

            # Removed min_trades check as requested

            sharpe = get_scalar(portfolio.sharpe_ratio())

            if np.isfinite(sharpe):
                results.append({
                    'volatility_window': volatility_window,
                    'volatility_momentum_window': volatility_momentum_window,
                    'volatility_momentum_threshold': volatility_momentum_threshold,
                    'higher_wma_window': higher_wma_window,
                    'sharpe': sharpe
                })
                if sharpe > best_sharpe:
                    best_sharpe = sharpe
                    best_params = (volatility_window, volatility_momentum_window, volatility_momentum_threshold, higher_wma_window)
        except Exception as e:
            # Skip invalid combo
            continue

    print("\n✅ Grid search terminé.")

    results_df = pd.DataFrame(results)
    if best_params is None:
        print("⚠️  Aucun paramètre valide trouvé – on retourne les premiers de la grille.")
        best_params = (
            CONFIG['optimization']['volatility_windows'][0],
            CONFIG['optimization']['volatility_momentum_windows'][0],
            CONFIG['optimization']['volatility_momentum_thresholds'][0],
            CONFIG['optimization']['higher_wma_windows'][0]
        )

    return best_params, results_df

# =============================================================================
# 5. PARAMETER OPTIMIZATION
# =============================================================================
def run_optimization(train_data, primary_timeframe, higher_timeframe=None):
    """
    Launch the brute-force grid search and returns best parameters and full result DF.
    """
    print("\n🔍 Running Grid Search Optimization...")

    best_params, results_df = grid_search_optimize(train_data, primary_timeframe, higher_timeframe)
 
    print(f"\n🧬 Selected params: volatility_window={best_params[0]}, volatility_momentum_window={best_params[1]}, volatility_momentum_threshold={best_params[2]:.2f}, higher_wma_window={best_params[3]}")
     
    return best_params, results_df


# =============================================================================
# 6. WALK-FORWARD OPTIMIZATION & ADVANCED PORTFOLIO ANALYSIS
# =============================================================================
def run_advanced_analysis(multi_data, train_data, test_data, best_params, primary_timeframe, higher_timeframe=None):
    """
    Runs advanced analysis including walk-forward, dynamic sizing, and overfitting checks.
    """
    print("\n🚀 Running advanced strategy analysis...")

    # Run strategy with different sizing methods
    train_portfolio, _ = run_backtest(train_data, best_params, primary_timeframe, higher_timeframe)
    test_portfolio, _ = run_backtest(test_data, best_params, primary_timeframe, higher_timeframe)
    full_portfolio, full_signals = run_backtest(multi_data, best_params, primary_timeframe, higher_timeframe)

    # Advanced analysis for each portfolio
    train_metrics = analyze_portfolio_advanced(train_portfolio, "Training")
    test_metrics = analyze_portfolio_advanced(test_portfolio, "Testing")
    full_metrics = analyze_portfolio_advanced(full_portfolio, "Full Strategy")

    # Overfitting detection
    overfitting_sharpe_diff = abs(train_metrics['sharpe'] - test_metrics['sharpe'])
    overfitting_return_diff = abs(train_metrics['return'] - test_metrics['return'])
    
    print(f"\n⚠️  Overfitting Analysis (Train vs. Test):")
    print(f"   Sharpe Ratio: {train_metrics['sharpe']:.3f} vs {test_metrics['sharpe']:.3f} (Diff: {overfitting_sharpe_diff:.3f})")
    print(f"   Total Return: {train_metrics['return']:.2f}% vs {test_metrics['return']:.2f}% (Diff: {overfitting_return_diff:.2f}%)")
    
    risk_level = 'HIGH' if overfitting_sharpe_diff > 1.0 or overfitting_return_diff > 50 else 'MODERATE' if overfitting_sharpe_diff > 0.5 or overfitting_return_diff > 25 else 'LOW'
    print(f"   Overfitting Risk Level: {risk_level}")


    # Walk-Forward Analysis using VectorBT concepts
    print("\n📈 Walk-Forward Analysis (Robustness Check)...")
    wf_results = walk_forward_analysis(multi_data, best_params, primary_timeframe, higher_timeframe)
    stability_ratio = np.inf
    if len(wf_results) > 0:
        print(f"📊 Walk-Forward Results:")
        print(wf_results[['window', 'sharpe', 'return', 'max_dd']].round(3))
        
        # Walk-forward stability check
        wf_sharpe_std = wf_results['sharpe'].std()
        stability_ratio = wf_sharpe_std / abs(wf_results['sharpe'].mean()) if wf_results['sharpe'].mean() != 0 else np.inf
        
        print(f"📈 Walk-Forward Stability Ratio: {stability_ratio:.3f}")
        print(f"   (Lower is better, < 0.5 is considered stable)")
        print(f"🎯 Stability Assessment: {'STABLE' if stability_ratio < 0.5 else 'UNSTABLE'}")


    # Dynamic Position Sizing Implementation
    print("\n💰 Position Sizing Analysis...")
    first_symbol = list(multi_data.keys())[0]
    print(f"   Current risk per trade: {CONFIG['portfolio']['risk_pct']*100:.2f}%")

    return train_metrics, test_metrics, full_metrics, full_portfolio, full_signals, stability_ratio, risk_level


def walk_forward_analysis(data, params, primary_timeframe, higher_timeframe=None, n_splits=5):
    """Implement walk-forward optimization."""
    # Utiliser le timeframe primaire pour le split des données
    primary_timeframe = CONFIG['data']['timeframes'][0] # Assurez-vous que cette ligne est correcte
    first_symbol_data = data[list(data.keys())[0]][primary_timeframe]
    total_length = len(first_symbol_data)
    window_size = total_length // n_splits
    
    wf_results = []
    
    for i in range(n_splits - 1):
        start_idx = i * window_size
        train_end_idx = start_idx + window_size
        test_end_idx = min(train_end_idx + window_size, total_length)
        
        # Create train/test splits
        test_dates = first_symbol_data.index[train_end_idx:test_end_idx]
        
        # We only need test data for evaluation in walk-forward analysis
        wf_test_data = {symbol: {primary_timeframe: df[primary_timeframe].loc[test_dates].copy()} for symbol, df in data.items()}
        
        try:
            # Test on this window
            wf_portfolio, _ = run_backtest(wf_test_data, params, primary_timeframe, higher_timeframe)
            wf_results.append({
                'window': i + 1,
                'start_date': test_dates[0],
                'end_date': test_dates[-1],
                'sharpe': get_scalar(wf_portfolio.sharpe_ratio()),
                'return': get_scalar(wf_portfolio.total_return()) * 100,
                'max_dd': get_scalar(wf_portfolio.max_drawdown()) * 100,
                'trades': len(wf_portfolio.trades.records_readable)
            })
        except Exception as e:
            print(f"Walk-forward window {i+1} failed: {e}")
            continue
    
    return pd.DataFrame(wf_results)


# Enhanced Portfolio Analysis with VectorBT Features
def analyze_portfolio_advanced(portfolio, name="Portfolio"):
    """Advanced portfolio analysis using VectorBT capabilities.
    This function is the single source of truth for portfolio statistics."""
    print(f"\n📊 {name} Advanced Analysis:")
    
    metrics = {}
    
    # Get returns and check volatility
    returns = portfolio.returns()
    if isinstance(returns, pd.DataFrame):
        returns = returns.iloc[:, 0]
    vol = returns.std() if len(returns) > 1 else 0
    if vol < 1e-6:
        print(f"⚠️ Volatilité très faible ({vol:.2e}) pour {name} - métriques ajustées à 0")
        metrics['sharpe'] = 0
        metrics['calmar'] = 0
    else:
        sharpe = get_scalar(portfolio.sharpe_ratio())
        metrics['sharpe'] = 0 if not np.isfinite(sharpe) else sharpe
        calmar = get_scalar(portfolio.calmar_ratio())
        metrics['calmar'] = 0 if not np.isfinite(calmar) else calmar

    metrics['return'] = get_scalar(portfolio.total_return()) * 100
    metrics['max_dd'] = get_scalar(portfolio.max_drawdown()) * 100
    
    print(f"   Sharpe Ratio: {metrics['sharpe']:.3f}")
    print(f"   Calmar Ratio: {metrics['calmar']:.3f}")
    print(f"   Total Return: {metrics['return']:.2f}%")
    print(f"   Max Drawdown: {metrics['max_dd']:.2f}%")
    
    # Trade analysis
    trades = portfolio.trades.records_readable
    metrics['trades'] = len(trades)
    
    if metrics['trades'] == 0:
        print("   ⚠️ No trades executed - metrics may be unreliable.")

    if metrics['trades'] > 0:
        wins = trades[trades['PnL'] > 0]
        losses = trades[trades['PnL'] < 0]
        
        metrics['win_rate'] = len(wins) / metrics['trades'] * 100 if metrics['trades'] > 0 else 0
        metrics['profit_factor'] = abs(wins['PnL'].sum() / losses['PnL'].sum()) if len(losses) > 0 and wins['PnL'].sum() > 0 else np.inf
        metrics['profit_factor'] = 0 if not np.isfinite(metrics['profit_factor']) else metrics['profit_factor']
        
        metrics['avg_win'] = wins['PnL'].mean() if len(wins) > 0 else 0
        metrics['avg_loss'] = losses['PnL'].mean() if len(losses) > 0 else 0
        metrics['win_loss_ratio'] = abs(metrics['avg_win'] / metrics['avg_loss']) if metrics['avg_loss'] != 0 else np.inf
        metrics['win_loss_ratio'] = 0 if not np.isfinite(metrics['win_loss_ratio']) else metrics['win_loss_ratio']

        print(f"   Win Rate: {metrics['win_rate']:.1f}%")
        print(f"   Profit Factor: {metrics['profit_factor']:.2f}")
        print(f"   Total Trades: {metrics['trades']}")
        print(f"   Average Win: ${metrics['avg_win']:.2f}")
        print(f"   Average Loss: ${metrics['avg_loss']:.2f}") # avg_loss is negative
        print(f"   Win/Loss Ratio: {metrics['win_loss_ratio']:.2f}")
    else:
        metrics.update({
            'win_rate': 0, 'profit_factor': 0, 'avg_win': 0, 
            'avg_loss': 0, 'win_loss_ratio': 0
        })
        print(f"   Total Trades: 0")

    # Risk metrics - add finite checks
    returns = portfolio.returns()
    if isinstance(returns, pd.DataFrame):
        returns = returns.iloc[:, 0]          # ou returns.mean(axis=1) pour agréger
    if len(returns) > 0:
        metrics['volatility'] = returns.std() * np.sqrt(252) * 100
        metrics['volatility'] = 0 if not np.isfinite(metrics['volatility']) else metrics['volatility']
        
        metrics['var_95'] = np.percentile(returns, 5) * 100
        metrics['var_95'] = 0 if not np.isfinite(metrics['var_95']) else metrics['var_95']
        
        metrics['var_99'] = np.percentile(returns, 1) * 100
        metrics['var_99'] = 0 if not np.isfinite(metrics['var_99']) else metrics['var_99']
        
        metrics['cvar_95'] = returns[returns <= np.percentile(returns, 5)].mean() * 100
        metrics['cvar_95'] = 0 if not np.isfinite(metrics['cvar_95']) else metrics['cvar_95']
        
        downside_vol = returns[returns < 0].std() * np.sqrt(252) * 100 if len(returns[returns < 0]) > 0 else 0
        metrics['downside_vol'] = 0 if not np.isfinite(downside_vol) else downside_vol
        
        print(f"   Volatility: {metrics['volatility']:.2f}%")
        print(f"   Downside Volatility: {metrics['downside_vol']:.2f}%")
        print(f"   VaR (95%): {metrics['var_95']:.2f}%")
        print(f"   VaR (99%): {metrics['var_99']:.2f}%")
        print(f"   CVaR (95%): {metrics['cvar_95']:.2f}%")
    else:
        metrics.update({
            'volatility': 0, 'var_95': 0, 'var_99': 0, 
            'cvar_95': 0, 'downside_vol': 0
        })
    
    return metrics

# =============================================================================
# 7. PERFORMANCE ANALYSIS
# =============================================================================
def analyze_performance(train_metrics, test_metrics, full_metrics):
    """
    Prints performance summary and detailed trade statistics.
    """
    print("\n📊 Performance Analysis:")

    # Create performance summary from pre-calculated metrics
    performance_data = {
        'Train': train_metrics,
        'Test': test_metrics,
        'Full': full_metrics
    }
    performance_df = pd.DataFrame(performance_data).loc[[
        'return', 'sharpe', 'max_dd', 'trades', 'win_rate'
    ]].rename(index={
        'return': 'Total Return [%]',
        'sharpe': 'Sharpe Ratio',
        'max_dd': 'Max Drawdown [%]',
        'trades': 'Total Trades',
        'win_rate': 'Win Rate [%]'
    })

    print(performance_df.round(3))

    # Display detailed trade statistics from full_metrics
    print(f"\n📈 Full Strategy Trade Statistics:")
    if full_metrics['trades'] > 0:
        print(f"Total Trades: {full_metrics['trades']}")
        print(f"Win Rate: {full_metrics['win_rate']:.1f}%")
        print(f"Average Win: ${full_metrics['avg_win']:.2f}")
        print(f"Average Loss: ${full_metrics['avg_loss']:.2f}")
        if full_metrics['win_loss_ratio'] != np.inf:
            print(f"Win/Loss Ratio: {full_metrics['win_loss_ratio']:.2f}")
    else:
        print("No trades were executed in the full backtest.")


# =============================================================================
# 8. ADVANCED VECTORBT VISUALIZATIONS & PORTFOLIO ANALYSIS (pour le timeframe primaire)
# =============================================================================
def plot_visualizations(full_portfolio, full_signals, multi_data, best_params, results_df, primary_timeframe):
    """
    Generates and shows all advanced visualizations.
    """
    print("\n📊 Creating advanced VectorBT visualizations...")
    
    # 1. Native VectorBT Portfolio Dashboard
    plot_portfolio_dashboard(full_portfolio)

    # 2. Multi-Asset Correlation Summary (Text Only)
    analyze_asset_correlation(multi_data)

    # 3. Parameter Sensitivity Summary (Text Only) - Removed as it's for Grid Search
    # summarize_parameter_sensitivity(results_df) 

    # 4. Signal Analysis Dashboard
    plot_signal_dashboard(multi_data, full_signals, best_params, primary_timeframe)

    # 5. PnL Distribution
    plot_pnl_distribution(full_portfolio)


def plot_portfolio_dashboard(full_portfolio):
    """Plots the primary portfolio dashboard."""
    print("🎯 Creating comprehensive portfolio dashboard...")
    full_portfolio.plot(
        subplots=['cum_returns', 'drawdowns','orders'],  # removed trade_pnl as it doesn't support grouped data
        template='plotly_dark',
        width=1400,
        height=800
    ).show()



def analyze_asset_correlation(multi_data):
    """Prints a summary of asset correlations."""
    print("📊 Multi-asset correlation analysis...")
    if len(multi_data) > 1:
        returns_data = {symbol: df['close'].pct_change() for symbol, df in multi_data.items()}
        returns_df = pd.DataFrame(returns_data).dropna()
        correlation_matrix = returns_df.corr()
        
        print("Asset Correlations:")
        for i, symbol1 in enumerate(correlation_matrix.columns):
            for j, symbol2 in enumerate(correlation_matrix.columns):
                if i < j:  # Only show upper triangle
                    corr_value = correlation_matrix.loc[symbol1, symbol2]
                    print(f"   {symbol1} vs {symbol2}: {corr_value:.3f}")


def summarize_parameter_sensitivity(results_df):
    """Prints a text summary of parameter sensitivity."""
    print("\n📊 Parameter sensitivity summary...")
    if results_df is not None and not results_df.empty:
        # Filter out non-finite Sharpe ratios for cleaner output
        finite_results = results_df[np.isfinite(results_df['sharpe'])].copy()
        
        if finite_results.empty:
            print("   No valid parameter combinations found with finite Sharpe ratios.")
            return




def plot_signal_dashboard(multi_data, full_signals, best_params, primary_timeframe):
    """Plots the signal analysis dashboard for the first symbol."""
    first_symbol = list(multi_data.keys())[0]
    first_symbol_data = multi_data[first_symbol][primary_timeframe]
    first_symbol_signals = full_signals[first_symbol]

    fig3 = make_subplots(
        rows=3, cols=1,
        shared_xaxes=True,
        subplot_titles=[f'{first_symbol} Price', 'Volatility Momentum', 'ATR'],
        vertical_spacing=0.05
    )

    # Price with signals
    fig3.add_trace(
        go.Scatter(x=first_symbol_data.index, y=first_symbol_data['close'], 
                   name='Price', line=dict(color='#00E396')),
        row=1, col=1
    )

    # Add entry/exit markers
    long_entries = first_symbol_signals['long_entries']
    short_entries = first_symbol_signals['short_entries']

    if long_entries.any():
        fig3.add_trace(
            go.Scatter(x=first_symbol_data.index[long_entries], 
                      y=first_symbol_data['close'][long_entries],
                      mode='markers', name='Long Entry', 
                      marker=dict(color='green', size=8, symbol='triangle-up')),
            row=1, col=1
        )

    if short_entries.any():
        fig3.add_trace(
            go.Scatter(x=first_symbol_data.index[short_entries], 
                      y=first_symbol_data['close'][short_entries],
                      mode='markers', name='Short Entry', 
                      marker=dict(color='red', size=8, symbol='triangle-down')),
            row=1, col=1
        )

    # Volatility Momentum
    fig3.add_trace(
        go.Scatter(x=first_symbol_data.index, y=first_symbol_signals['volatility_momentum'], 
                   name='Volatility Momentum', line=dict(color='orange')),
        row=2, col=1
    )

    # Add threshold lines
    fig3.add_hline(y=CONFIG['indicators']['volatility_momentum_threshold'], line_dash="dash", line_color="green", row=2, col=1)
    fig3.add_hline(y=CONFIG['indicators']['volatility_reversal_threshold'], line_dash="dash", line_color="red", row=2, col=1)

    # Add WMA to price chart
    fig3.add_trace(
        go.Scatter(x=first_symbol_data.index, y=first_symbol_data['wma'], 
                   name='WMA', line=dict(color='blue')),
        row=1, col=1
    )

    # ATR
    atr_col = f"ATRr_{CONFIG['indicators']['atr_length']}"
    fig3.add_trace(
        go.Scatter(x=first_symbol_data.index, y=first_symbol_data[atr_col], 
                   name='ATR', line=dict(color='purple')),
        row=3, col=1
    )

    fig3.update_layout(
        template="plotly_dark",
        height=800,
        title="Volatility Momentum Strategy Dashboard",
        showlegend=True
    )
    fig3.show()


def plot_pnl_distribution(full_portfolio):
    """Plots the distribution of trade PnL."""
    trades = full_portfolio.trades.records_readable
    if not trades.empty:
        try:
            pnl_fig = go.Figure()
            pnl_fig.add_trace(go.Histogram(
                x=trades['PnL'],
                nbinsx=30,
                name='Trade PnL Distribution',
                opacity=0.7
            ))
            pnl_fig.add_vline(x=0, line_dash="dash", line_color="white", 
                            annotation_text="Break-even")
            pnl_fig.update_layout(
                title="Trade PnL Distribution",
                xaxis_title="PnL ($)",
                yaxis_title="Frequency",
                template="plotly_dark",
                height=400
            )
            pnl_fig.show()
        except Exception as e:
            print(f"PnL distribution plot error: {e}")

# =============================================================================
# 9. FINAL SUMMARY & RECOMMENDATION
# =============================================================================
def final_summary(full_metrics, stability_ratio, overfitting_risk_level, multi_data, primary_timeframe):
    """
    Prints the final summary and recommendation based on performance.
    """
    first_symbol = list(multi_data.keys())[0]
    buy_hold = multi_data[first_symbol][primary_timeframe]['close'] / multi_data[first_symbol][primary_timeframe]['close'].iloc[0] * CONFIG['portfolio']['init_cash']
    final_return = full_metrics['return']
    market_return = (buy_hold.iloc[-1] / buy_hold.iloc[0] - 1) * 100

    print("\n" + "="*50)
    print("🎯 FINAL STRATEGY SUMMARY")
    print("="*50)
    print(f"Strategy Return:     {final_return:>8.2f}%")
    print(f"Market Return:       {market_return:>8.2f}%")
    print(f"Alpha (vs Market):   {final_return - market_return:>8.2f}%")
    print(f"Sharpe Ratio:        {full_metrics['sharpe']:>8.3f}")
    print(f"Max Drawdown:        {full_metrics['max_dd']:>8.2f}%")
    print(f"Total Trades:        {full_metrics['trades']:>8}")

    # Comprehensive scoring system
    score = 0
    criteria = []

    # Handle nan/inf in metrics for scoring
    sharpe = 0 if not np.isfinite(full_metrics['sharpe']) else full_metrics['sharpe']
    calmar = 0 if not np.isfinite(full_metrics['calmar']) else full_metrics['calmar']

    # Sharpe Ratio (using safe value)
    if sharpe > 1.5:
        score += 3
        criteria.append(f"✅ Excellent Sharpe ratio ({sharpe:.2f} > 1.5)")
    elif sharpe > 1.0:
        score += 2
        criteria.append(f"✅ Good Sharpe ratio ({sharpe:.2f} > 1.0)")
    elif sharpe > 0.5:
        score += 1
        criteria.append(f"⚠️ Moderate Sharpe ratio ({sharpe:.2f} > 0.5)")
    else:
        criteria.append(f"❌ Poor Sharpe ratio ({sharpe:.2f} < 0.5)")

    # Calmar Ratio
    if calmar > 1.0:
        score += 2
        criteria.append(f"✅ Strong Calmar ratio ({calmar:.2f} > 1.0)")
    elif calmar > 0.5:
        score += 1
        criteria.append(f"⚠️ Moderate Calmar ratio ({calmar:.2f} > 0.5)")
    else:
        criteria.append(f"❌ Weak Calmar ratio ({calmar:.2f} < 0.5)")

    # Walk-forward stability
    if stability_ratio < 0.5:
        score += 2
        criteria.append(f"✅ Stable walk-forward performance (Ratio: {stability_ratio:.2f})")
    elif stability_ratio < 1.0:
        score += 1
        criteria.append(f"⚠️ Moderate walk-forward stability (Ratio: {stability_ratio:.2f})")
    else:
        criteria.append(f"❌ Unstable walk-forward performance (Ratio: {stability_ratio:.2f})")

    # Overfitting Risk
    if overfitting_risk_level == 'LOW':
        score += 2
        criteria.append("✅ Low overfitting risk")
    elif overfitting_risk_level == 'MODERATE':
        score += 1
        criteria.append("⚠️ Moderate overfitting risk")
    else:
        criteria.append("❌ High overfitting risk")

    # Alpha Generation
    if (final_return - market_return) > 5:
        score += 2
        criteria.append("✅ Strong alpha generation (>5%)")
    elif final_return > market_return:
        score += 1
        criteria.append("⚠️ Positive alpha generation")
    else:
        criteria.append("❌ Negative alpha generation")

    # Determine recommendation
    if score >= 9:
        final_rec = "🟢 STRONGLY RECOMMENDED: Excellent performance and robustness."
    elif score >= 7:
        final_rec = "🟡 RECOMMENDED: Good strategy with minor concerns."
    elif score >= 5:
        final_rec = "🟠 CONDITIONAL: Strategy shows promise but needs improvement."
    else:
        final_rec = "🔴 NOT RECOMMENDED: Strategy fails multiple critical criteria."

    print(f"\nFinal Score: {score}/11")
    print(f"Recommendation: {final_rec}")
    print("\nCriteria Assessment Breakdown:")
    for criterion in criteria:
        print(f"  {criterion}")


# =============================================================================
# 10. VECTORBT ADVANCED FEATURES IMPLEMENTATION (pour le timeframe primaire)
# =============================================================================
def run_vbt_advanced_analysis(full_portfolio, multi_data, full_metrics, primary_timeframe):
    """
    Runs and prints a variety of advanced analyses using vectorbt features.
    """
    print("\n🚀 VectorBT Advanced Features Analysis")
    print("="*50)

    trades = full_portfolio.trades.records_readable

    # 1. Portfolio Resampling for Different Timeframes
    print("📊 Multi-Timeframe Resampling Analysis...")
    try:
        equity = full_portfolio.value()
        
        daily_equity = equity.resample('D').last().dropna()
        weekly_equity = equity.resample('W').last().dropna()
        monthly_equity = equity.resample('ME').last().dropna() # Fixed 'M' to 'ME'
        
        daily_returns = daily_equity.pct_change().dropna()
        weekly_returns = weekly_equity.pct_change().dropna()
        monthly_returns = monthly_equity.pct_change().dropna()
        
        daily_sharpe = daily_returns.mean() / daily_returns.std() * np.sqrt(252) if daily_returns.std() > 0 else 0
        weekly_sharpe = weekly_returns.mean() / weekly_returns.std() * np.sqrt(52) if weekly_returns.std() > 0 else 0
        monthly_sharpe = monthly_returns.mean() / monthly_returns.std() * np.sqrt(12) if monthly_returns.std() > 0 else 0
        
        print(f"Daily Sharpe:    {daily_sharpe:.3f}")
        print(f"Weekly Sharpe:   {weekly_sharpe:.3f}")
        print(f"Monthly Sharpe:  {monthly_sharpe:.3f}")
        
    except Exception as e:
        print(f"Resampling error: {e}")

    # 2. Rolling Performance Metrics
    print("\n📈 Rolling Performance Analysis...")
    try:
        returns = full_portfolio.returns()
        if isinstance(returns, pd.DataFrame):
            returns = returns.iloc[:, 0]          # ou returns.mean(axis=1) pour agréger
        rolling_window = min(252, len(returns) // 4)
        if rolling_window > 20:
            rolling_sharpe = returns.rolling(rolling_window).apply(
                lambda x: x.mean() / x.std() * np.sqrt(252) if x.std() > 0 else 0
            )
            
            print(f"Rolling Sharpe Mean: {get_scalar(rolling_sharpe.mean()):.3f}")
            print(f"Rolling Sharpe Std:  {get_scalar(rolling_sharpe.std()):.3f}")
            print(f"Rolling Sharpe Min:  {get_scalar(rolling_sharpe.min()):.3f}")
            print(f"Rolling Sharpe Max:  {get_scalar(rolling_sharpe.max()):.3f}")
            
    except Exception as e:
        print(f"Rolling analysis error: {e}")

    # 3. Drawdown Analysis with VectorBT
    print("\n📉 Advanced Drawdown Analysis...")
    try:
        dd = full_portfolio.drawdowns
        print(f"Max Drawdown:        {get_scalar(dd.max_drawdown()) * 100:.2f}%")
        print(f"Avg Drawdown:        {get_scalar(dd.avg_drawdown()) * 100:.2f}%")
        print(f"Max DD Duration:     {get_scalar(dd.max_duration())}")
        print(f"Avg DD Duration:     {get_scalar(dd.avg_duration())}")
        
    except Exception as e:
        print(f"Drawdown analysis error: {e}")

    # 4. Trade Analysis with VectorBT
    print("\n💼 Advanced Trade Analysis...")
    if not trades.empty:
        try:
            if 'Duration' in trades.columns:
                avg_duration = trades['Duration'].mean()
                max_duration = trades['Duration'].max()
                min_duration = trades['Duration'].min()
                print(f"Avg Trade Duration:  {avg_duration}")
                print(f"Max Trade Duration:  {max_duration}")
                print(f"Min Trade Duration:  {min_duration}")
            
        except Exception as e:
            print(f"Trade duration analysis error: {e}")

    # 5. Risk Metrics with VectorBT (already in full_metrics)
    print("\n⚠️  Advanced Risk Metrics...")
    try:
        if not trades.empty and 'MAE' in trades.columns:
            max_mae = trades['MAE'].max()
            avg_mae = trades['MAE'].mean()
            print(f"Max Adverse Excursion: ${max_mae:.2f}")
            print(f"Avg Adverse Excursion: ${avg_mae:.2f}")
        
        print(f"Volatility:          {full_metrics['volatility']:.2f}%")
        print(f"Downside Volatility: {full_metrics['downside_vol']:.2f}%")
        print(f"VaR (95%):           {full_metrics['var_95']:.2f}%")
        print(f"VaR (99%):           {full_metrics['var_99']:.2f}%")
        print(f"CVaR (95%):          {full_metrics['cvar_95']:.2f}%")
        
    except Exception as e:
        print(f"Risk metrics error: {e}")

    # 6. Performance Attribution
    print("\n🎯 Performance Attribution...")
    try:
        if len(multi_data) > 1 and not trades.empty:
            symbol_pnl = trades.groupby('Column')['PnL'].sum()
            total_pnl = symbol_pnl.sum()
            
            if total_pnl != 0:
                print("Symbol Contributions:")
                for symbol, pnl in symbol_pnl.items():
                    contribution_pct = (pnl / total_pnl * 100)
                    print(f"   {symbol}: ${pnl:.2f} ({contribution_pct:.1f}%)")
                    
    except Exception as e:
        print(f"Attribution error: {e}")

    print("="*50)
    print("✅ VectorBT-Enhanced Analysis Complete!")


def timeframe_to_pandas_freq(tf: str) -> str:
    """Convert custom timeframe strings to pandas offset aliases accepted by VectorBT.
    Examples:
    'daily' -> '1D', '1d' -> '1D', 'hourly' -> '1H', '1h' -> '1H'.
    For already valid aliases, returns as-is."""
    tf_lower = tf.lower()
    mapping = {
        'daily': '1D', '1d': '1D', 'd': '1D', 'day': '1D',
        'hourly': '1h', '1h': '1h', 'h': '1h',
        '30min': '30T', '15min': '15T', '5min': '5T', '1min': '1T'
    }
    return mapping.get(tf_lower, tf)


def main():
    """
    Main function to run the backtesting and analysis pipeline.
    """
    # 3. DATA LOADING
    print("\n" + "="*80)
    print("📊 CHARGEMENT DES DONNÉES DEPUIS CSV")
    print("="*80)
    csv_dir = CONFIG['data'].get('csv_directory', 'data/historical')
    print(f"Source: Fichiers CSV locaux depuis '{csv_dir}'")
    print(f"Format de nom de fichier attendu: SYMBOL_INTERVAL.csv (ex: EURUSD_1H_2020-2024.csv)")
    
    multi_data = {}
 
    # Boucle de chargement pour tous les symboles et tous les timeframes définis
    for symbol in CONFIG['data']['symbols']:
        multi_data[symbol] = {}
        for timeframe in CONFIG['data']['timeframes']:
            data = fetch_data_from_csv(symbol, timeframe)
            if not data.empty:
                multi_data[symbol][timeframe] = add_indicators(data)
            else:
                print(f"⚠️ Aucune donnée chargée pour {symbol} sur le timeframe {timeframe}. Ce symbole/timeframe sera ignoré.")
                # Nettoyage si un timeframe est vide pour un symbole
                if not multi_data[symbol]: # Si le dictionnaire du symbole est vide après ce timeframe
                    del multi_data[symbol] # Supprimer le symbole complètement
                break # Passer au symbole suivant si un timeframe est manquant
     
    if not multi_data:
        print("\n❌ ERREUR: Aucune donnée n'a été chargée.")
        print("\nConseils de dépannage:")
        print(f"1. Vérifiez que les fichiers CSV existent dans le répertoire '{csv_dir}'.")
        print("2. Assurez-vous que les noms de fichiers correspondent aux symboles et à l'intervalle dans CONFIG.")
        print("3. Vérifiez que les fichiers CSV contiennent au minimum les colonnes 'open', 'high', 'low', 'close' avec une date en index.")
        return

    print(f"\n✅ Données chargées avec succès pour {len(multi_data)} symboles")
    
    # Déterminer le timeframe primaire pour les opérations de backtest et d'optimisation
    # Pour l'instant, nous utilisons le premier timeframe défini dans la configuration.
    if not CONFIG['data']['timeframes']:
        print("\n❌ ERREUR: Aucun timeframe défini dans CONFIG['data']['timeframes'].")
        return
    primary_timeframe = CONFIG['data']['timeframes'][0]
    print(f"💡 Timeframe primaire pour la stratégie: {primary_timeframe}")

    # Déterminer le higher timeframe si défini
    higher_timeframe = CONFIG['data'].get('higher_timeframe', None)
    print(f"💡 Filtre cross-timeframe: {'Actif sur ' + higher_timeframe if higher_timeframe else 'Désactivé'}")

    # Harmonisation de la plage temporelle sur TOUS les DataFrames (symboles et timeframes)
    all_dataframes = []
    for symbol_data in multi_data.values():
        all_dataframes.extend(symbol_data.values())

    if all_dataframes:
        common_index = None
        for df in all_dataframes:
            common_index = df.index if common_index is None else common_index.intersection(df.index)

        if common_index is None or common_index.empty:
            print("\n❌ Les datasets ne partagent pas de plage de dates commune – impossible de synchroniser.")
            return

        # Tronquer tous les DataFrames à l'index commun
        for symbol in list(multi_data.keys()):
            for timeframe in list(multi_data[symbol].keys()):
                multi_data[symbol][timeframe] = multi_data[symbol][timeframe].loc[common_index].copy()

        print(f"\n📏 Plage temporelle harmonisée pour tous les timeframes: {common_index[0].date()} → {common_index[-1].date()}  ({len(common_index)} barres)")
     
    # Split data for validation
    try:

        # Utiliser le timeframe primaire pour le split des données
        first_symbol = list(multi_data.keys())[0]
        first_symbol_data_for_split = multi_data[first_symbol][primary_timeframe]

        split_idx = int(len(first_symbol_data_for_split) * CONFIG['optimization']['split_ratio'])
        split_date = first_symbol_data_for_split.index[split_idx]

        train_data = {}
        test_data = {}
        for symbol, timeframe_data in multi_data.items():
            train_data[symbol] = {}
            test_data[symbol] = {}
            for timeframe, df in timeframe_data.items():
                train_data[symbol][timeframe] = df.loc[df.index < split_date].copy()
                test_data[symbol][timeframe] = df.loc[df.index >= split_date].copy()


        print(f"📊 Data split ({primary_timeframe}): {len(train_data[first_symbol][primary_timeframe])} train, {len(test_data[first_symbol][primary_timeframe])} test")

        # 5. PARAMETER OPTIMIZATION
        best_params, results_df = run_optimization(train_data, primary_timeframe, higher_timeframe)
        if best_params is None:
            print("❌ L'optimisation n'a pas trouvé de paramètres valides.")
            return

        # 6. ADVANCED ANALYSIS
        train_metrics, test_metrics, full_metrics, full_portfolio, full_signals, stability_ratio, overfitting_risk_level = run_advanced_analysis(multi_data, train_data, test_data, best_params, primary_timeframe, higher_timeframe)

        # 7. PERFORMANCE ANALYSIS
        analyze_performance(train_metrics, test_metrics, full_metrics)
        
        # 9. FINAL SUMMARY (Moved up for quick overview)

        # Passer le timeframe primaire pour le calcul du rendement du marché
        final_summary(full_metrics, stability_ratio, overfitting_risk_level, multi_data, primary_timeframe)

        # 8. VISUALIZATIONS

        # Les visualisations doivent aussi savoir quel timeframe afficher si multiples
        plot_visualizations(full_portfolio, full_signals, multi_data, best_params, results_df, primary_timeframe)
        
        # 10. ADVANCED VBT ANALYSIS
        run_vbt_advanced_analysis(full_portfolio, multi_data, full_metrics, primary_timeframe)
    
    except Exception as e:
        print(f"\n❌ Une erreur s'est produite lors de l'exécution: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()