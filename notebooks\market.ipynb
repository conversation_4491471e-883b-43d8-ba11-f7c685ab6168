import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.getcwd(), '..')))
from exchange import hyperliquid, fetch_and_prepare_data
import numpy as np
import pandas as pd

print(hyperliquid.load_markets().keys())
data = fetch_and_prepare_data("BTC/USDC:USDC")
print(data.head())


# Split data into train and test sets (80/20 split)

split = 5
data_splits = {}  # Dictionnaire pour stocker les splits

for i in range(split):
    data_splits[f'data_split_{i}'] = np.array_split(data, split)[i]

figs = {}
for col in ['close']:
    plot_data = pd.concat([data_splits[f'data_split_{i}'][col].rename(f"data_split_{i}_{col}") for i in range(split)], axis=1)
    figs[col] = plot_data.plot(title=f"{col} par split")

# figs['close'].show()


import vectorbt as vbt
vbt.settings.set_theme('dark')
# vbt.settings['plotting']['layout']['width'] = 1200
# vbt.settings['plotting']['layout']['height'] = 200
pf = vbt.Portfolio.from_signals(
    close=data["close"],
    init_cash=10000,
    fees=0.001,
    slippage=0.001,
    freq="1h",
    tp_stop=0.02,
    )
 
# To print available subplots for a VectorBT Portfolio, use:
print("Available subplots:", pf.subplots.keys())
pf.plot()






import sys
import os

# Add the parent directory to sys.path so we can import exchange
# In Jupyter, __file__ is not defined; use os.getcwd() instead:
sys.path.append(os.path.abspath(os.path.join(os.getcwd(), '..')))

from exchange import hyperliquid, fetch_and_prepare_data

data = hyperliquid.fetch_ohlcv("BTC/USDC:USDC", "1h", "2023-01-01", limit=1000)

data.head()
